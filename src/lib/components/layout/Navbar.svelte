<script lang="ts">
	import { page } from '$app/state';
	import { themeStore } from '$lib/stores/theme';

	// Get page title from data or derive from route
	const pageTitle = $derived(() => {
		if (page.data?.title) return page.data.title;

		const path = page.url.pathname;
		if (path === '/' || path === '/home') return 'Dashboard';
		if (path.includes('categorias')) return 'Categories';
		if (path.includes('marcas')) return 'Brands';
		if (path.includes('drive')) return 'Drive';
		if (path.includes('productos')) return 'Products';
		if (path.includes('inventario')) return 'Inventory';
		if (path.includes('ingresos')) return 'Income';
		if (path.includes('ventas')) return 'Sales';
		return 'Faztore Admin';
	});

	function toggleTheme() {
		themeStore.toggle();
	}
</script>

<header class="fixed">
	<nav class="surface-container">
		<!-- Mobile menu button -->
		<button class="circle transparent" data-ui="#sidebar" aria-label="Toggle menu">
			<i>menu</i>
		</button>

		<!-- Page title -->
		<h5 class="max no-margin">{pageTitle()}</h5>

		<!-- Actions -->
		<nav class="no-space">
			<!-- Search button -->
			<button class="circle transparent" aria-label="Search">
				<i>search</i>
			</button>

			<!-- Notifications -->
			<button class="circle transparent" aria-label="Notifications">
				<i>notifications</i>
			</button>

			<!-- Theme toggle -->
			<button class="circle transparent" onclick={toggleTheme} aria-label="Toggle theme">
				<i>{themeStore.effectiveTheme === 'dark' ? 'light_mode' : 'dark_mode'}</i>
			</button>

			<!-- User menu -->
			<button class="circle transparent" aria-label="User menu">
				<i>account_circle</i>
			</button>
		</nav>
	</nav>
</header>
