<script lang="ts">
	import { page } from '$app/state';
	import { themeStore } from '$lib/stores/theme';

	// Get page title from data or derive from route
	const pageTitle = $derived(() => {
		if (page.data?.title) return page.data.title;

		const path = page.url.pathname;
		if (path === '/' || path === '/home') return 'Dashboard';
		if (path.includes('categorias')) return 'Categories';
		if (path.includes('marcas')) return 'Brands';
		if (path.includes('drive')) return 'Drive';
		if (path.includes('productos')) return 'Products';
		if (path.includes('inventario')) return 'Inventory';
		if (path.includes('ingresos')) return 'Income';
		if (path.includes('ventas')) return 'Sales';
		return 'Faztore Admin';
	});

	function toggleTheme() {
		themeStore.toggle();
	}
</script>

<!-- Top Navigation following BeerCSS pattern -->
<nav class="top">
	<!-- Mobile menu button -->
	<button class="circle transparent l" data-ui="#sidebar" aria-label="Toggle menu">
		<i>menu</i>
	</button>

	<!-- Brand/Logo for mobile -->
	<div class="s m">
		<i class="primary">store</i>
	</div>

	<!-- Page title -->
	<h5 class="max no-margin">{pageTitle()}</h5>

	<!-- Actions -->
	<button class="circle transparent" aria-label="Search">
		<i>search</i>
	</button>

	<button class="circle transparent" aria-label="Notifications">
		<i>notifications</i>
	</button>

	<button class="circle transparent" onclick={toggleTheme} aria-label="Toggle theme">
		<i>{themeStore.effectiveTheme === 'dark' ? 'light_mode' : 'dark_mode'}</i>
	</button>

	<button class="circle transparent" aria-label="User menu">
		<i>account_circle</i>
	</button>
</nav>
