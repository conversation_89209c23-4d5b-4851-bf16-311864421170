/* Faztore Admin - Material Design Theme Customization */

/* Custom theme colors for Faztore brand */
:root,
body.light {
	--primary: #f8bc49;
	--on-primary: #422d00;
	--primary-container: #5e4100;
	--on-primary-container: #ffdea9;
	--secondary: #dac3a1;
	--on-secondary: #3c2e16;
	--secondary-container: #54442a;
	--on-secondary-container: #f7dfbb;
	--tertiary: #b3cea6;
	--on-tertiary: #203619;
	--tertiary-container: #364d2e;
	--on-tertiary-container: #cfebc0;
	--error: #ba1a1a;
	--on-error: #ffffff;
	--error-container: #ffdad6;
	--on-error-container: #410002;
	--background: #fffbff;
	--on-background: #1c1b1e;
	--surface: #fdf8fd;
	--on-surface: #1c1b1e;
	--surface-variant: #e7e0eb;
	--on-surface-variant: #49454e;
	--outline: #7a757f;
	--outline-variant: #cac4cf;
	--shadow: #000000;
	--scrim: #000000;
	--inverse-surface: #313033;
	--inverse-on-surface: #f4eff4;
	--inverse-primary: #cfbcff;
	--surface-dim: #ddd8dd;
	--surface-bright: #fdf8fd;
	--surface-container-lowest: #ffffff;
	--surface-container-low: #f7f2f7;
	--surface-container: #f2ecf1;
	--surface-container-high: #ece7eb;
	--surface-container-highest: #e6e1e6;
}

body.dark {
	--primary: #f8bc49;
	--on-primary: #422d00;
	--primary-container: #5e4100;
	--on-primary-container: #ffdea9;
	--secondary: #dac3a1;
	--on-secondary: #3c2e16;
	--secondary-container: #54442a;
	--on-secondary-container: #f7dfbb;
	--tertiary: #b3cea6;
	--on-tertiary: #203619;
	--tertiary-container: #364d2e;
	--on-tertiary-container: #cfebc0;
	--error: #ffb4ab;
	--on-error: #690005;
	--error-container: #93000a;
	--on-error-container: #ffb4ab;
	--background: #1f1b16;
	--on-background: #eae1d9;
	--surface: #16130e;
	--on-surface: #eae1d9;
	--surface-variant: #4e4639;
	--on-surface-variant: #d1c5b4;
	--outline: #9a8f80;
	--outline-variant: #4e4639;
	--shadow: #000000;
	--scrim: #000000;
	--inverse-surface: #eae1d9;
	--inverse-on-surface: #34302a;
	--inverse-primary: #7d5800;
	--surface-dim: #16130e;
	--surface-bright: #3d3833;
	--surface-container-lowest: #110e09;
	--surface-container-low: #1f1b16;
	--surface-container: #231f1a;
	--surface-container-high: #2d2924;
	--surface-container-highest: #38342e;
}

/* Reduce shadow intensity for cleaner look */
:root {
	--elevate1: 0 1px 3px rgba(0, 0, 0, 0.02), 0 1px 2px rgba(0, 0, 0, 0.04);
	--elevate2: 0 1px 5px rgba(0, 0, 0, 0.02), 0 2px 4px rgba(0, 0, 0, 0.04);
	--elevate3: 0 4px 8px rgba(0, 0, 0, 0.02), 0 2px 6px rgba(0, 0, 0, 0.04);
}

/* Ensure proper font setup */
:root {
	--font: 'Roboto Flex', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
	--font-icon: 'Material Symbols Outlined';
}

/* Global layout improvements */
body {
	font-family: var(--font);
	background-color: var(--background);
	color: var(--on-background);
}

/* Ensure proper responsive behavior */
.responsive {
	margin-left: auto;
	margin-right: auto;
}

/* Fix drawer positioning for left sidebar */
nav.left.drawer {
	position: fixed;
	top: 0;
	left: 0;
	height: 100vh;
	width: 280px;
	z-index: 1000;
}

/* Adjust main content when sidebar is present */
@media (min-width: 1024px) {
	main.responsive {
		margin-left: 280px;
	}
}

/* Ensure header spans full width */
header.fixed {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
}

@media (min-width: 1024px) {
	header.fixed {
		left: 280px;
	}
}

/* Add proper spacing for fixed header */
main.responsive {
	padding-top: 80px;
}