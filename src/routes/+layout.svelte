<script lang="ts">
	let { children } = $props();
	import 'beercss';
	import 'material-dynamic-colors';
	import '../styles.css';
	import { browser } from '$app/environment';
	import { onMount } from 'svelte';

	// Initialize BeerCSS UI system
	onMount(() => {
		if (browser && typeof window !== 'undefined') {
			// Initialize BeerCSS UI system
			const script = document.createElement('script');
			script.src = 'https://cdn.jsdelivr.net/npm/beercss@3.6.13/dist/cdn/beer.min.js';
			script.onload = () => {
				// Set initial theme mode
				if ((window as any).ui) {
					const savedTheme = localStorage.getItem('theme') || 'light';
					(window as any).ui('mode', savedTheme);
				}
			};
			document.head.appendChild(script);
		}
	});
</script>

{@render children()}
