<script lang="ts">
	import type { LayoutProps } from '../$types';
	import { page } from '$app/state';
	import { themeStore } from '$lib/stores/theme';
	import Background from '$lib/components/background.svelte';
	import Navbar from '$lib/components/layout/Navbar.svelte';
	import Sidebar from '$lib/components/layout/Sidebar.svelte';

	let { children }: LayoutProps = $props();

	// Check if we're on the auth page
	const isAuthPage = $derived(page.url.pathname.startsWith('/auth'));
</script>

<svelte:head>
	<meta name="theme-color" content={themeStore.effectiveTheme === 'dark' ? '#1c1b1e' : '#fffbff'} />
</svelte:head>

{#if isAuthPage}
	<!-- Auth layout - full screen with background -->
	<div class="fill middle-align center-align">
		<Background opacity={0.05} maxOpacity={0.15} zIndex="back" />
		{@render children()}
	</div>
{:else}
	<!-- Main admin layout -->
	<div class="fill">
		<Background opacity={0.03} maxOpacity={0.08} zIndex="back" />

		<!-- Sidebar Navigation -->
		<Sidebar />

		<!-- Main Content Area -->
		<main class="responsive">
			<!-- Top Navigation Bar -->
			<Navbar />

			<!-- Page Content -->
			<div class="padding">
				{@render children()}
			</div>
		</main>
	</div>
{/if}
