<script lang="ts">
	// Dashboard stats
	const stats = [
		{ title: 'Total Products', value: '1,234', icon: 'inventory_2', color: 'primary' },
		{ title: 'Categories', value: '45', icon: 'category', color: 'secondary' },
		{ title: 'Brands', value: '23', icon: 'branding_watermark', color: 'tertiary' },
		{ title: 'Sales Today', value: '$2,456', icon: 'point_of_sale', color: 'primary' }
	];

	const recentActivities = [
		{
			action: 'New product added',
			item: 'iPhone 15 Pro',
			time: '2 minutes ago',
			icon: 'add_circle'
		},
		{ action: 'Stock updated', item: 'Samsung Galaxy S24', time: '15 minutes ago', icon: 'update' },
		{ action: 'Category created', item: 'Smartphones', time: '1 hour ago', icon: 'category' },
		{ action: 'Sale completed', item: 'Order #1234', time: '2 hours ago', icon: 'check_circle' }
	];
</script>

<svelte:head>
	<title>Dashboard - Faztore Admin</title>
</svelte:head>

<!-- Dashboard Container -->
<div class="padding">
	<!-- Welcome Section -->
	<div class="large-space">
		<h3 class="no-margin">Welcome back!</h3>
		<p class="secondary-text">Here's what's happening with your store today.</p>
	</div>

	<!-- Stats Grid -->
	<div class="grid">
		{#each stats as stat (stat.title)}
			<div class="s12 m6 l3">
				<article class="surface-container round padding elevate">
					<div class="row">
						<div class="max">
							<h6 class="small-text secondary-text no-margin">{stat.title}</h6>
							<h4 class="no-margin bold">{stat.value}</h4>
						</div>
						<div class="circle {stat.color}">
							<i class="large">{stat.icon}</i>
						</div>
					</div>
				</article>
			</div>
		{/each}
	</div>

	<div class="large-space"></div>

	<!-- Content Grid -->
	<div class="grid">
		<!-- Recent Activity -->
		<div class="s12 m12 l6">
			<article class="surface-container round padding elevate">
				<header class="row">
					<h5 class="max no-margin">Recent Activity</h5>
					<button class="circle transparent small">
						<i>more_vert</i>
					</button>
				</header>

				<div class="space"></div>

				{#each recentActivities as activity (activity.item + activity.time)}
					<div class="row small-space">
						<div class="circle primary-container">
							<i class="primary">{activity.icon}</i>
						</div>
						<div class="max">
							<p class="no-margin small-text bold">{activity.action}</p>
							<p class="no-margin small-text secondary-text">{activity.item}</p>
							<small class="secondary-text">{activity.time}</small>
						</div>
					</div>
				{/each}

				<div class="space"></div>
				<div class="center-align">
					<a href="/activity" class="link">View all activity</a>
				</div>
			</article>
		</div>

		<!-- Quick Actions -->
		<div class="s12 m12 l6">
			<article class="surface-container round padding elevate">
				<header>
					<h5 class="no-margin">Quick Actions</h5>
				</header>

				<div class="space"></div>

				<div class="grid">
					<div class="s6">
						<a
							href="/productos/new"
							class="surface-container-high round padding center-align block"
						>
							<i class="large primary">add_box</i>
							<div class="small-text">Add Product</div>
						</a>
					</div>
					<div class="s6">
						<a
							href="/categorias/new"
							class="surface-container-high round padding center-align block"
						>
							<i class="large secondary">create_new_folder</i>
							<div class="small-text">New Category</div>
						</a>
					</div>
					<div class="s6">
						<a href="/inventario" class="surface-container-high round padding center-align block">
							<i class="large tertiary">inventory</i>
							<div class="small-text">Check Stock</div>
						</a>
					</div>
					<div class="s6">
						<a href="/ventas/new" class="surface-container-high round padding center-align block">
							<i class="large primary">point_of_sale</i>
							<div class="small-text">New Sale</div>
						</a>
					</div>
				</div>
			</article>
		</div>
	</div>
</div>
