<script lang="ts">
	// Dashboard stats
	const stats = [
		{ title: 'Total Products', value: '1,234', icon: 'inventory_2', color: 'primary' },
		{ title: 'Categories', value: '45', icon: 'category', color: 'secondary' },
		{ title: 'Brands', value: '23', icon: 'branding_watermark', color: 'tertiary' },
		{ title: 'Sales Today', value: '$2,456', icon: 'point_of_sale', color: 'primary' }
	];

	const recentActivities = [
		{
			action: 'New product added',
			item: 'iPhone 15 Pro',
			time: '2 minutes ago',
			icon: 'add_circle'
		},
		{ action: 'Stock updated', item: 'Samsung Galaxy S24', time: '15 minutes ago', icon: 'update' },
		{ action: 'Category created', item: 'Smartphones', time: '1 hour ago', icon: 'category' },
		{ action: 'Sale completed', item: 'Order #1234', time: '2 hours ago', icon: 'check_circle' }
	];
</script>

<svelte:head>
	<title>Dashboard - Faztore Admin</title>
</svelte:head>

<!-- Dashboard following BeerCSS patterns -->
<div class="page">
	<!-- Welcome Section -->
	<div class="padding">
		<h3 class="no-margin">Welcome back!</h3>
		<p class="secondary-text">Here's what's happening with your store today.</p>
	</div>

	<div class="space"></div>

	<!-- Stats Grid - Perfect responsive layout -->
	<div class="grid">
		{#each stats as stat (stat.title)}
			<div class="s12 m6 l3">
				<article class="surface-container round padding stat-card">
					<nav class="no-space">
						<div class="max">
							<div class="small-text secondary-text">{stat.title}</div>
							<h4 class="no-margin bold">{stat.value}</h4>
						</div>
						<div class="circle {stat.color}">
							<i>{stat.icon}</i>
						</div>
					</nav>
				</article>
			</div>
		{/each}
	</div>

	<div class="large-space"></div>

	<!-- Content Grid -->
	<div class="grid">
		<!-- Recent Activity -->
		<div class="s12 m12 l6">
			<article class="surface-container round padding">
				<nav class="no-space">
					<h5 class="max no-margin">Recent Activity</h5>
					<button class="circle transparent small">
						<i>more_vert</i>
					</button>
				</nav>

				<div class="space"></div>

				{#each recentActivities as activity (activity.item + activity.time)}
					<nav class="no-space small-space">
						<div class="circle primary-container">
							<i class="primary">{activity.icon}</i>
						</div>
						<div class="max">
							<div class="small-text bold">{activity.action}</div>
							<div class="small-text secondary-text">{activity.item}</div>
							<div class="tiny-text secondary-text">{activity.time}</div>
						</div>
					</nav>
				{/each}

				<div class="space"></div>
				<div class="center-align">
					<a href="/activity" class="link">View all activity</a>
				</div>
			</article>
		</div>

		<!-- Quick Actions -->
		<div class="s12 m12 l6">
			<article class="surface-container round padding">
				<h5 class="no-margin">Quick Actions</h5>

				<div class="space"></div>

				<div class="grid">
					<div class="s6">
						<a
							href="/productos/new"
							class="surface-container-high round padding center-align block action-item"
						>
							<i class="large primary">add_box</i>
							<div class="small-text">Add Product</div>
						</a>
					</div>
					<div class="s6">
						<a
							href="/categorias/new"
							class="surface-container-high round padding center-align block action-item"
						>
							<i class="large secondary">create_new_folder</i>
							<div class="small-text">New Category</div>
						</a>
					</div>
					<div class="s6">
						<a
							href="/inventario"
							class="surface-container-high round padding center-align block action-item"
						>
							<i class="large tertiary">inventory</i>
							<div class="small-text">Check Stock</div>
						</a>
					</div>
					<div class="s6">
						<a
							href="/ventas/new"
							class="surface-container-high round padding center-align block action-item"
						>
							<i class="large primary">point_of_sale</i>
							<div class="small-text">New Sale</div>
						</a>
					</div>
				</div>
			</article>
		</div>
	</div>

	<!-- Additional Content Section -->
	<div class="large-space"></div>

	<div class="grid">
		<!-- Sales Chart Placeholder -->
		<div class="s12 m12 l8">
			<article class="surface-container round padding">
				<h5 class="no-margin">Sales Overview</h5>
				<div class="space"></div>
				<div class="center-align secondary-text">
					<i class="extra">trending_up</i>
					<div>Chart will be implemented here</div>
				</div>
			</article>
		</div>

		<!-- Top Products -->
		<div class="s12 m12 l4">
			<article class="surface-container round padding">
				<h5 class="no-margin">Top Products</h5>
				<div class="space"></div>

				<nav class="no-space small-space">
					<div class="circle primary-container">
						<i class="primary">smartphone</i>
					</div>
					<div class="max">
						<div class="small-text bold">iPhone 15 Pro</div>
						<div class="tiny-text secondary-text">234 sales</div>
					</div>
					<div class="small-text bold">$234K</div>
				</nav>

				<nav class="no-space small-space">
					<div class="circle secondary-container">
						<i class="secondary">laptop</i>
					</div>
					<div class="max">
						<div class="small-text bold">MacBook Pro</div>
						<div class="tiny-text secondary-text">156 sales</div>
					</div>
					<div class="small-text bold">$156K</div>
				</nav>

				<nav class="no-space small-space">
					<div class="circle tertiary-container">
						<i class="tertiary">headphones</i>
					</div>
					<div class="max">
						<div class="small-text bold">AirPods Pro</div>
						<div class="tiny-text secondary-text">89 sales</div>
					</div>
					<div class="small-text bold">$89K</div>
				</nav>
			</article>
		</div>
	</div>
</div>
